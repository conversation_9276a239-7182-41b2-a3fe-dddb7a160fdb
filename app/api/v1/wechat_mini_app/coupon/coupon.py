# -*- coding: utf-8 -*-
# 优惠券模块

from fastapi import APIRouter, Depends, HTTPException, Header
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.coupon import coupon_service
from app.service.order import OrderService
from app.dao.order import order_dao
from app.utils.logger import logger
from app.schemas.coupon import CouponPricingRequest, CouponPricingResponse

router = APIRouter()

@router.get("/user-coupons")
async def get_user_coupons(
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的所有优惠券列表（不依赖订单）
    
    Args:
        token: 用户认证token
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 包含三个优惠券列表的响应：
        - available: 在有效期内且可使用的优惠券列表
        - expired: 未在有效期内的优惠券列表（包括已过期和未到期的）
        - used: 已经使用的优惠券列表
    """
    try:
        # 验证token
        if not token:
            logger.warning("获取用户优惠券列表：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"获取用户优惠券列表：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 调用优惠券服务获取优惠券列表
        logger.info(f"开始获取用户优惠券列表: user_id={user.id}")
        coupon_lists = coupon_service.get_user_coupons(db, user)

        # 构建响应数据
        response_data = {
            "status": 200,
            "message": "获取成功",
            "data": {
                "available_coupons": coupon_lists["available"],
                "unavailable_coupons": coupon_lists["unavailable"],
                "used_coupons": coupon_lists["used"],
                "summary": {
                    "available_count": len(coupon_lists["available"]),
                    "unavailable_count": len(coupon_lists["unavailable"]),
                    "used_count": len(coupon_lists["used"]),
                    "total_count": (
                        len(coupon_lists["available"]) + 
                        len(coupon_lists["unavailable"]) + 
                        len(coupon_lists["used"])
                    )
                },
                "user_info": {
                    "user_id": user.id,
                    "username": user.username,
                    "phone": user.phone
                }
            }
        }

        logger.info(f"用户优惠券列表获取成功: user_id={user.id}, "
                   f"可用={len(coupon_lists['available'])}, "
                   f"不可用={len(coupon_lists['unavailable'])}, "
                   f"已使用={len(coupon_lists['used'])}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取用户优惠券列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取优惠券列表失败: {str(e)}", "status": 500}
        )

@router.post("/coupon-pricing")
async def coupon_pricing(
    request: CouponPricingRequest,
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    基于优惠券的计价接口

    根据用户选择的产品和优惠券，计算最终的订单金额和优惠详情

    Args:
        request: 计价请求，包含用户ID、产品组合、优惠券使用记录ID列表
        token: 用户认证token
        db: 数据库会话

    Returns:
        Dict[str, Any]: 计价结果，包含订单信息、优惠信息、优惠券列表等
    """
    try:
        # 验证token
        if not token:
            logger.warning("优惠券计价：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"优惠券计价：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户ID是否匹配
        if request.user_id != user.id:
            logger.warning(f"优惠券计价：用户ID不匹配, token_user_id: {user.id}, request_user_id: {request.user_id}")
            raise HTTPException(
                status_code=403,
                detail={"message": "用户ID不匹配", "status": 403}
            )

        # 验证请求参数
        if not request.products:
            raise HTTPException(
                status_code=400,
                detail={"message": "产品列表不能为空", "status": 400}
            )

        # 先通过OrderService的pre_order生成order_items
        logger.info(f"开始生成预订单: user_id={request.user_id}, "
                   f"products_count={len(request.products)}")

        # 将ProductQuantityItem转换为OrderService.pre_order需要的格式
        products_for_pre_order = []
        for product_item in request.products:
            products_for_pre_order.append({
                "product_id": product_item.product_id,
                "quantity": product_item.quantity
            })

        # 调用pre_order生成order_items
        pre_order_result = OrderService.pre_order(
            session=db,
            products=products_for_pre_order
        )

        # 调用优惠券计价服务
        logger.info(f"开始优惠券计价: user_id={request.user_id}, "
                   f"order_items_count={len(pre_order_result.order_items)}, "
                   f"coupons_count={len(request.coupon_usage_record_ids)}")

        pricing_result = coupon_service.coupon_pricing(
            session=db,
            user_id=request.user_id,
            order_items=pre_order_result.order_items,
            coupon_usage_record_ids=request.coupon_usage_record_ids
        )

        # 构建响应数据
        response_data = {
            "status": 200,
            "message": "计价成功",
            "data": pricing_result
        }

        logger.info(f"优惠券计价成功: user_id={request.user_id}, "
                   f"total_amount={pricing_result['pricing_result']['order']['total_amount']}, "
                   f"payable_amount={pricing_result['pricing_result']['order']['payable_amount']}, "
                   f"total_discount={pricing_result['pricing_result']['discount']['total_discount']}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as e:
        # 业务逻辑验证失败
        logger.warning(f"优惠券计价验证失败: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={"message": str(e), "status": 400}
        )
    except Exception as e:
        logger.error(f"优惠券计价失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"计价失败: {str(e)}", "status": 500}
        )
